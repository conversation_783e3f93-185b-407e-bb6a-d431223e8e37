<script setup lang="ts">
import { ref, watch } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-vue-next'
import {
  usePharmacyRequestStore,
  pharmacyBillingAddressSchema,
  type PharmacyBillingAddressPayload,
} from '@central/stores/pharmacyRequestStore'
import type { PharmacyRequestDetail } from '@central/types/pharmacyRequest'
import { storeToRefs } from 'pinia'

const props = defineProps<{
  open: boolean
  pharmacyDetail: PharmacyRequestDetail | null
}>()

const emit = defineEmits(['update:open', 'updated'])

const pharmacyRequestStore = usePharmacyRequestStore()
const { isUpdatingBillingAddress, error, fieldErrors } = storeToRefs(pharmacyRequestStore)
const { updatePharmacyBillingAddress } = pharmacyRequestStore

const formError = ref('')

const formSchema = toTypedSchema(pharmacyBillingAddressSchema)

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    zipcode: '',
  },
})

const closeModal = () => {
  emit('update:open', false)
  form.resetForm()
  formError.value = ''
}

watch(
  () => props.pharmacyDetail,
  (newDetail) => {
    if (newDetail && props.open && newDetail.address_details) {
      form.setValues({
        address_line_1: newDetail.address_details.address_line_1 || '',
        address_line_2: newDetail.address_details.address_line_2 || '',
        city: newDetail.address_details.city || '',
        state: newDetail.address_details.state || '',
        zipcode: newDetail.address_details.zipcode || '',
      })
    }
  },
  { immediate: true },
)

// Watch for field errors from store
watch(
  fieldErrors,
  (errors) => {
    if (errors && Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          form.setFieldError(field as keyof PharmacyBillingAddressPayload, messages[0])
        }
      })
    }
  },
  { deep: true },
)

const onSubmit = form.handleSubmit(async (values) => {
  if (!props.pharmacyDetail?.id) return

  formError.value = ''

  const payload = {
    id: props.pharmacyDetail.id,
    address_line_1: values.address_line_1,
    address_line_2: values.address_line_2 || '',
    city: values.city,
    state: values.state,
    zipcode: values.zipcode,
  }

  const success = await updatePharmacyBillingAddress(payload)

  if (success) {
    emit('updated')
    closeModal()
  } else if (error.value) {
    formError.value = error.value
  }
})
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>Edit Billing Address</DialogTitle>
      </DialogHeader>

      <form @submit="onSubmit" class="space-y-6">
        <Alert v-if="formError" variant="destructive">
          <AlertDescription>{{ formError }}</AlertDescription>
        </Alert>

        <div class="grid grid-cols-1 gap-4">
          <FormField v-slot="{ componentField }" name="address_line_1">
            <FormItem>
              <FormLabel>Address Line 1</FormLabel>
              <FormControl>
                <Input type="text" placeholder="Enter street address" v-bind="componentField" />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="address_line_2">
            <FormItem>
              <FormLabel>Address Line 2 (Optional)</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Apartment, suite, unit, building, floor, etc."
                  v-bind="componentField"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField v-slot="{ componentField }" name="city">
              <FormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter city" v-bind="componentField" />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>

            <FormField v-slot="{ componentField }" name="state">
              <FormItem>
                <FormLabel>State</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter state" v-bind="componentField" />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField v-slot="{ componentField }" name="zipcode">
              <FormItem>
                <FormLabel>ZIP Code</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter ZIP code" v-bind="componentField" />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            @click="closeModal"
            :disabled="isUpdatingBillingAddress"
          >
            Cancel
          </Button>
          <Button type="submit" :disabled="isUpdatingBillingAddress">
            <Loader2 v-if="isUpdatingBillingAddress" class="h-4 w-4 animate-spin mr-2" />
            Update Address
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
