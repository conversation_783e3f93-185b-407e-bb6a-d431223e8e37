<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-vue-next'
import {
  usePharmacyRequestStore,
  pharmacyDetailsSchema,
  type PharmacyDetailPayload,
} from '@central/stores/pharmacyRequestStore'
import type { PharmacyRequestDetail } from '@central/types/pharmacyRequest'
import { storeToRefs } from 'pinia'
import { vMaska } from 'maska/vue'

const props = defineProps<{
  open: boolean
  pharmacyDetail: PharmacyRequestDetail | null
}>()

const emit = defineEmits(['update:open', 'updated'])

const pharmacyRequestStore = usePharmacyRequestStore()
const { isUpdatingDetails, error, fieldErrors } = storeToRefs(pharmacyRequestStore)
const { updatePharmacyDetails } = pharmacyRequestStore

const formError = ref('')

const formSchema = toTypedSchema(pharmacyDetailsSchema)

const form = useForm({
  validationSchema: formSchema,
  initialValues: {
    pharmacy_name: '',
    preferred_subdomain: '',
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
  },
})

const closeModal = () => {
  emit('update:open', false)
  form.resetForm()
  formError.value = ''
}

watch(
  () => props.pharmacyDetail,
  (newDetail) => {
    if (newDetail && props.open) {
      form.setValues({
        pharmacy_name: newDetail.pharmacy_name || '',
        preferred_subdomain: newDetail.preferred_sub_domain || '',
        first_name: newDetail.first_name || '',
        last_name: newDetail.last_name || '',
        email: newDetail.email || '',
        phone_number: newDetail.phone_number || '',
      })
    }
  },
  { immediate: true },
)

watch(
  fieldErrors,
  (errors) => {
    if (errors && Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, messages]) => {
        if (Array.isArray(messages) && messages.length > 0) {
          form.setFieldError(field as keyof PharmacyDetailPayload, messages[0])
        }
      })
    }
  },
  { deep: true },
)

const onSubmit = form.handleSubmit(async (values) => {
  if (!props.pharmacyDetail?.id) return

  formError.value = ''

  const payload = {
    id: props.pharmacyDetail.id,
    pharmacy_name: values.pharmacy_name,
    preferred_subdomain: values.preferred_subdomain,
    first_name: values.first_name,
    last_name: values.last_name,
    email: values.email,
    phone_number: values.phone_number.replace(/\D/g, ''), // Remove non-digits
  }

  const success = await updatePharmacyDetails(payload)

  if (success) {
    emit('updated')
    closeModal()
  } else if (error.value) {
    formError.value = error.value
  }
})

const centralDomain = computed(() => {
  return import.meta.env.VITE_TENANCY_CENTRAL_DOMAINS || window.location.host
})

const subdomainPreview = computed(() => {
  const subdomain = form.values.preferred_subdomain
  if (!subdomain) return ''

  const domain = centralDomain.value
  if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
    return `http://${subdomain}.${domain}`
  }
  return `https://${subdomain}.${domain}`
})
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>Edit Pharmacy Details</DialogTitle>
      </DialogHeader>

      <form @submit="onSubmit" class="space-y-6">
        <Alert v-if="formError" variant="destructive">
          <AlertDescription>{{ formError }}</AlertDescription>
        </Alert>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="md:col-span-2">
            <FormField v-slot="{ componentField }" name="pharmacy_name">
              <FormItem>
                <FormLabel>Pharmacy Name</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter pharmacy name" v-bind="componentField" />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <div class="md:col-span-2">
            <FormField v-slot="{ componentField }" name="preferred_subdomain">
              <FormItem>
                <FormLabel>Preferred Subdomain</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter subdomain" v-bind="componentField" />
                </FormControl>
                <FormMessage />
                <div v-if="subdomainPreview" class="text-sm text-muted-foreground mt-1">
                  Preview: <span class="font-mono">{{ subdomainPreview }}</span>
                </div>
              </FormItem>
            </FormField>
          </div>

          <FormField v-slot="{ componentField }" name="first_name">
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input type="text" placeholder="Enter first name" v-bind="componentField" />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="last_name">
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input type="text" placeholder="Enter last name" v-bind="componentField" />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="email">
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Enter email address" v-bind="componentField" />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField v-slot="{ componentField }" name="phone_number">
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input
                  type="tel"
                  placeholder="(*************"
                  v-maska
                  data-maska="(###) ###-####"
                  v-bind="componentField"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" @click="closeModal" :disabled="isUpdatingDetails">
            Cancel
          </Button>
          <Button type="submit" :disabled="isUpdatingDetails">
            <Loader2 v-if="isUpdatingDetails" class="h-4 w-4 animate-spin mr-2" />
            Update Details
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
