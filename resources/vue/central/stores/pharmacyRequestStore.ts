import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@central/composables'
import type { ApiResponse, PaginationResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type {
  PharmacyItem,
  PharmacyRequestDetail,
  PharmacyListPayload,
  PaymentLogDetail,
} from '@central/types/pharmacyRequest'
import { z } from 'zod'
import { toast } from 'vue-sonner'

export const pharmacyDetailsSchema = z.object({
  pharmacy_name: z.string().min(1, 'Pharmacy name is required'),
  preferred_subdomain: z.string().optional(),
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  phone_number: z
    .string()
    .min(1, 'Phone number is required')
    .refine((val) => val.replace(/\D/g, '').length === 10, {
      message: 'Phone number must be 10 digits',
    }),
})

export const pharmacyBillingAddressSchema = z.object({
  address_line_1: z.string().min(1, 'Address is required'),
  address_line_2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipcode: z.string().min(1, 'ZIP code is required'),
})

export type PharmacyDetailPayload = z.infer<typeof pharmacyDetailsSchema>

export type PharmacyBillingAddressPayload = z.infer<typeof pharmacyBillingAddressSchema>

export const usePharmacyRequestStore = defineStore('pharmacyRequestStore', () => {
  const pharmacies = ref<PharmacyItem[]>([])
  const pharmacyDetail = ref<PharmacyRequestDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetail = ref(false)
  const paymentLogDetail = ref<PaymentLogDetail | null>(null)
  const isLoadingPaymentLogDetail = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  // const statusFilter = ref<string>('all')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })
  const isUpdatingDetails = ref(false)
  const isUpdatingBillingAddress = ref(false)

  const listPayload = computed<Partial<PharmacyListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    // is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  async function fetchPharmacyList() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { pharmacies: PaginationResponse<PharmacyItem> }
      >('/list/pharmacy/registration', listPayload.value)

      if (response.data.status === 200) {
        const pagedData = response.data.pharmacies
        pharmacies.value = pagedData.records

        pagination.value = {
          currentPage: pagedData.current_page,
          perPage: pagedData.per_page,
          totalPages: pagedData.totalPage,
          totalRecords: pagedData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch pharmacies'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacies:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacies.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function fetchPharmacyDetail(id: string) {
    isLoadingDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & { pharmacyDetails: PharmacyRequestDetail }
      >(`/view/pharmacy/registration/details/${id}`)

      if (response.data.status === 200) {
        pharmacyDetail.value = response.data.pharmacyDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch pharmacy with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacy details.')
      return false
    } finally {
      isLoadingDetail.value = false
    }
  }

  async function fetchPaymentLogDetail(id: string) {
    isLoadingPaymentLogDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { paymentLogDetails: PaymentLogDetail }>(
        `/view/payment/log/${id}`,
      )

      if (response.data.status === 200) {
        paymentLogDetail.value = response.data.paymentLogDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch payment log with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching payment log details:', err)
      error.value = processErrors(err, 'An error occurred while fetching payment log details.')
      return false
    } finally {
      isLoadingPaymentLogDetail.value = false
    }
  }

  async function updatePharmacyDetails(
    payload: PharmacyDetailPayload & {
      id: string
    },
  ) {
    isUpdatingDetails.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>(
        '/update/pharmacy/registration/details',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy details updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update pharmacy details'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy details.')
      return false
    } finally {
      isUpdatingDetails.value = false
    }
  }

  async function updatePharmacyBillingAddress(
    payload: PharmacyBillingAddressPayload & {
      id: string
    },
  ) {
    isUpdatingBillingAddress.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>(
        '/update/pharmacy/registration/billing/details',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy billing address updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update pharmacy billing address'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy billing address:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy billing address.')
      return false
    } finally {
      isUpdatingBillingAddress.value = false
    }
  }

  return {
    pharmacies,
    pagination,
    isLoadingList,
    isLoadingDetail,
    error,
    fieldErrors,
    searchQuery,
    // statusFilter,
    sortBy,
    pharmacyDetail,
    paymentLogDetail,
    isLoadingPaymentLogDetail,
    isUpdatingDetails,
    isUpdatingBillingAddress,
    fetchPharmacyList,
    fetchPharmacyDetail,
    fetchPaymentLogDetail,
    updatePharmacyDetails,
    updatePharmacyBillingAddress,
  }
})
