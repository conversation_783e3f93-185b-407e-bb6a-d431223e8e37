import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@central/composables'
import type { ApiResponse, PaginationResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type {
  ContactItem,
  ContactListPayload,
  ContactDetails,
  PharmacyItem,
} from '@central/types/contacts'

export const useContactsStore = defineStore('contactsStore', () => {
  const contacts = ref<ContactItem[]>([])
  const isLoadingList = ref(false)
  const contactDetail = ref<ContactDetails | null>(null)
  const isLoadingDetail = ref(false)
  const pharmacyOptions = ref<PharmacyItem[]>([])
  const isLoadingPharmacyOptions = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const selectedPharmacyId = ref<string>('all')
  const searchQuery = ref('')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const listPayload = computed<Partial<ContactListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    pharmacy_id: selectedPharmacyId.value !== 'all' ? selectedPharmacyId.value : '',
  }))

  async function fetchContactList() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { contacts: PaginationResponse<ContactItem> }
      >('/contacts', listPayload.value)

      if (response.data.status === 200) {
        const pagedData = response.data.contacts
        contacts.value = pagedData.records

        pagination.value = {
          currentPage: pagedData.current_page,
          perPage: pagedData.per_page,
          totalPages: pagedData.totalPage,
          totalRecords: pagedData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch contacts'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching contacts:', err)
      error.value = processErrors(err, 'An error occurred while fetching contacts.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function fetchContactDetail(id: string) {
    isLoadingDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { contactDetails: ContactDetails }>(
        `/view/contact/details/${id}`,
      )

      if (response.data.status === 200) {
        contactDetail.value = response.data.contactDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch contact with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching contact details:', err)
      error.value = processErrors(err, 'An error occurred while fetching contact details.')
      return false
    } finally {
      isLoadingDetail.value = false
    }
  }

  async function fetchPharmacyOptions() {
    isLoadingPharmacyOptions.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { pharmacies: PharmacyItem[] }>(
        '/list/pharmacies',
      )

      if (response.data.status === 200) {
        pharmacyOptions.value = response.data.pharmacies
        return true
      } else {
        error.value = response.data.message || `Failed to fetch pharmacy options.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacy details.')
      return false
    } finally {
      isLoadingPharmacyOptions.value = false
    }
  }

  return {
    contacts,
    pagination,
    isLoadingList,
    isLoadingDetail,
    error,
    fieldErrors,
    searchQuery,
    sortBy,
    contactDetail,
    selectedPharmacyId,
    pharmacyOptions,
    isLoadingPharmacyOptions,
    fetchContactList,
    fetchContactDetail,
    fetchPharmacyOptions,
  }
})
