import type { LucideIcon } from 'lucide-vue-next'
import { LayoutDashboard, Building2, Settings2, Contact } from 'lucide-vue-next'

export interface SubMenuItem {
  title: string
  routeName: string
}

export interface MenuItem {
  title: string
  routeName?: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SubMenuItem[]
}

export interface MenuGroup {
  label?: string
  items: MenuItem[]
}

export type MenuConfig = MenuGroup[]

export const menuItems: MenuConfig = [
  {
    label: 'Platform',
    items: [
      {
        title: 'Dashboard',
        routeName: 'dashboard',
        icon: LayoutDashboard,
        isActive: true,
      },
      {
        title: 'Pharmacies',
        icon: Building2,
        items: [
          {
            title: 'Pharmacies',
            routeName: 'pharmacies-list',
          },
          {
            title: 'Onboarding Requests',
            routeName: 'pharmacy-requests-list',
          },
          {
            title: 'Failed Requests',
            routeName: 'pharmacy-failed-list',
          },
        ],
      },
      {
        title: 'Plan Settings',
        routeName: 'plan-settings',
        icon: Settings2,
      },
      {
        title: 'Contacts',
        routeName: 'contacts-list',
        icon: Contact,
      },
    ],
  },
]
