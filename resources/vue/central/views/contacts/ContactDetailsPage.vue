<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import { useContactsStore } from '@central/stores'
import { storeToRefs } from 'pinia'
import { formatPhone, formatCurrency } from '@/lib'

const contactsStore = useContactsStore()
const { contactDetail, isLoadingDetail, error } = storeToRefs(contactsStore)
const { fetchContactDetail } = contactsStore

const router = useRouter()
const route = useRoute()
const contactId = computed(() => route.params.id as string)

async function loadContactDetails() {
  if (contactId.value) {
    await fetchContactDetail(contactId.value)
  }
}

onMounted(() => {
  loadContactDetails()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3 mb-6">
      <Button variant="ghost" size="sm" @click="router.back()">
        <ArrowLeft class="h-4 w-4" />
        <span>Back</span>
      </Button>
      <h1 class="text-2xl font-bold">Contact Details</h1>
    </div>

    <div v-if="isLoadingDetail" class="flex items-center justify-center py-8">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading contact details...</span>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <p class="text-destructive">{{ error }}</p>
      <Button @click="loadContactDetails" class="mt-4"> Try Again </Button>
    </div>

    <div v-else-if="contactDetail" class="space-y-6">
      <!-- Basic Information -->
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Name</label>
              <p class="text-sm">{{ contactDetail.name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Email</label>
              <p class="text-sm">{{ contactDetail.email }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Phone Number</label>
              <p class="text-sm">{{ formatPhone(contactDetail.phone_number) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Contacted On</label>
              <p class="text-sm">{{ contactDetail.created_date }} at {{ contactDetail.created_time }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Product Details -->
      <Card>
        <CardHeader>
          <CardTitle>Product Details</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Product Name</label>
              <p class="text-sm">{{ contactDetail.product_detail.product_name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Title</label>
              <p class="text-sm">{{ contactDetail.product_detail.product_title }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Strength</label>
              <p class="text-sm">{{ contactDetail.product_detail.product_strength }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Quantity</label>
              <p class="text-sm">{{ contactDetail.product_detail.product_qty }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Form</label>
              <p class="text-sm">{{ contactDetail.product_detail.product_form }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Price</label>
              <p class="text-sm">{{ formatCurrency(contactDetail.product_detail.price) }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Category</label>
              <p class="text-sm">{{ contactDetail.product_detail.main_category_name }}</p>
            </div>
            <div v-if="contactDetail.product_detail.sub_category_name">
              <label class="text-sm font-medium text-muted-foreground">Sub Category</label>
              <p class="text-sm">{{ contactDetail.product_detail.sub_category_name }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Pharmacy Details -->
      <Card>
        <CardHeader>
          <CardTitle>Pharmacy Details</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Pharmacy Name</label>
              <p class="text-sm">{{ contactDetail.pharmacy_details.pharmacy_name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Admin Name</label>
              <p class="text-sm">
                {{ contactDetail.pharmacy_details.first_name }} {{ contactDetail.pharmacy_details.last_name }}
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Email</label>
              <p class="text-sm">{{ contactDetail.pharmacy_details.email }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Phone Number</label>
              <p class="text-sm">{{ formatPhone(contactDetail.pharmacy_details.phone_number) }}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
