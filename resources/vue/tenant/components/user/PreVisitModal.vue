<script setup lang="ts">
import { processErrors, isEmptyObject } from '@/lib'
import type { ApiResponse } from '@/types/api'
import { apiClient } from '@tenant/composables'
import { ref, watch } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-vue-next'
import { vMaska } from 'maska/vue'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'

const props = defineProps<{
  open: boolean
  productItemId: string
  xpedicareUrl: string
}>()

const emit = defineEmits(['update:open'])

const userLeadSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  phone_number: z.string().min(1, 'Phone number is required'),
  product_item_id: z.string().min(1, 'Product item is required'),
})

type UserLead = z.infer<typeof userLeadSchema>

const formError = ref<string | null>(null)
const isSubmitting = ref(false)

const form = useForm<UserLead>({
  validationSchema: toTypedSchema(userLeadSchema),
  initialValues: {
    name: '',
    email: '',
    phone_number: '',
    product_item_id: '',
  },
})

// Watch for prop changes and update form
watch(
  () => props.productItemId,
  (newProductItemId) => {
    if (newProductItemId) {
      form.setFieldValue('product_item_id', newProductItemId)
    }
  },
  { immediate: true },
)

// Reset form when modal opens
watch(
  () => props.open,
  (isOpen) => {
    if (isOpen) {
      form.resetForm()
      formError.value = null
      if (props.productItemId) {
        form.setFieldValue('product_item_id', props.productItemId)
      }
    }
  },
)

function applyBackendErrors(errors: Record<string, any>) {
  if (!errors || typeof errors !== 'object') return

  Object.entries(errors).forEach(([field, messages]) => {
    if (Array.isArray(messages) && messages.length > 0) {
      form.setFieldError(field as keyof UserLead, messages[0])
    } else if (typeof messages === 'string') {
      form.setFieldError(field as keyof UserLead, messages)
    }
  })
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isSubmitting.value) return

  isSubmitting.value = true
  formError.value = null

  try {
    const payload = {
      ...values,
      phone_number: values.phone_number.replace(/\D/g, ''),
    }

    const response = await apiClient.post<ApiResponse>('/user-contact-details', payload)

    if (response.data.status === 200) {
      if (props.xpedicareUrl) {
        window.location.href = props.xpedicareUrl
      }
    } else if (response.data.errors && !isEmptyObject(response.data.errors)) {
      applyBackendErrors(response.data.errors)
      isSubmitting.value = false
    } else {
      formError.value = response.data.message || 'Something went wrong. Please try again.'
      isSubmitting.value = false
    }
  } catch (error: any) {
    // Handle network errors or other exceptions
    if (error.response?.data?.errors && !isEmptyObject(error.response.data.errors)) {
      applyBackendErrors(error.response.data.errors)
    } else {
      formError.value = processErrors(error, 'Something went wrong. Please try again.')
    }
    isSubmitting.value = false
  }
})

const closeModal = () => {
  emit('update:open', false)
}
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[500px] bg-gradient-to-br from-cyan-50 to-white">
      <DialogHeader>
        <DialogTitle
          class="text-2xl font-bold bg-gradient-to-r from-amber-500 to-amber-600 bg-clip-text text-transparent"
          >Start Your Treatment</DialogTitle
        >
        <DialogDescription>
          Enter your details to begin your personalized treatment experience and receive expert
          guidance.
        </DialogDescription>
      </DialogHeader>

      <div class="mt-4">
        <form @submit.prevent="onSubmit" class="space-y-6">
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <div class="space-y-4">
            <FormField name="name" v-slot="{ componentField, errorMessage }">
              <FormItem>
                <FormLabel>Full Name *</FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    type="text"
                    placeholder="Enter your full name"
                    class="bg-white h-11"
                    :aria-invalid="!!errorMessage"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>

            <FormField name="email" v-slot="{ componentField, errorMessage }">
              <FormItem>
                <FormLabel>Email Address *</FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    type="email"
                    placeholder="Enter your email address"
                    class="bg-white h-11"
                    :aria-invalid="!!errorMessage"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>

            <FormField name="phone_number" v-slot="{ componentField, errorMessage }">
              <FormItem>
                <FormLabel>Phone Number *</FormLabel>
                <FormControl>
                  <Input
                    v-bind="componentField"
                    v-maska
                    data-maska="(###) ###-####"
                    type="tel"
                    placeholder="Enter your phone number"
                    class="bg-white h-11"
                    :aria-invalid="!!errorMessage"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <div class="flex gap-3 pt-4">
            <Button
              type="submit"
              size="lg"
              :disabled="isSubmitting"
              class="flex-1 bg-black text-white hover:bg-gray-800 disabled:opacity-50"
            >
              <Loader2 v-if="isSubmitting" class="w-4 h-4 mr-2 animate-spin" />
              {{ isSubmitting ? 'Submitting...' : 'Continue' }}
            </Button>
          </div>
        </form>
      </div>
    </DialogContent>
  </Dialog>
</template>
