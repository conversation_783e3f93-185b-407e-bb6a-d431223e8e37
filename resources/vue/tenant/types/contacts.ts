export type ContactItem = {
  id: string
  name: string
  email: string
  phone_number: string
  product_detail: {
    main_category_name: string
    sub_category_name: string | null
    product_name: string
    product_title: string
    product_strength: string
    product_type: string
    product_qty: string
    product_form: string
    price: number
  }
  created_date: string
  created_time: string
}

export type ContactListPayload = {
  searchQuery?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type ContactDetails = {
  id: string
  name: string
  email: string
  phone_number: string
  product_detail: {
    main_category_name: string
    sub_category_name: string | null
    product_name: string
    product_title: string
    product_strength: string
    product_type: string
    product_qty: string
    product_form: string
    price: number
    xpedicare_url: string
    image: string
  }
  created_date: string
  created_time: string
}
