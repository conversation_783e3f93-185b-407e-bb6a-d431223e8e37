<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Search, PlusIcon, XIcon, Trash2Icon, Eye } from 'lucide-vue-next'
import { useProductStore } from '@tenant/stores'
import type { AcceptableValue } from 'reka-ui'
import { useDebounce } from '@vueuse/core'
import ConfirmationDialog from '@/components/ConfirmationDialog.vue'
import { toast } from 'vue-sonner'
import { formatCurrency } from '@/lib'
import { IconBox } from '@tabler/icons-vue'

const productStore = useProductStore()
const {
  products,
  isLoadingList,
  pagination,
  isDeleting,
  isTogglingStatus,
  error: storeError,
  searchQuery,
  statusFilter,
  sortBy,
  mainCategoryFilter,
  categoryOptions,
} = storeToRefs(productStore)
const { fetchProducts, toggleProductStatus, deleteProduct, fetchCategoryOptions } = productStore

const router = useRouter()
const debouncedSearchQuery = useDebounce(searchQuery, 300)
const selectedProductId = ref<string>('')
const isDeleteDialogOpen = ref(false)

const handleSearch = async () => {
  pagination.value.currentPage = 1
  await fetchProducts()
}

const handleSort = async (column: string) => {
  if (sortBy.value.column === column) {
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value.column = column
    sortBy.value.direction = 'desc'
  }
  await fetchProducts()
}

const handlePageChange = async (page: number) => {
  pagination.value.currentPage = page
  await fetchProducts()
}

const handlePerPageChange = async (value: AcceptableValue) => {
  pagination.value.perPage = Number(value)
  pagination.value.currentPage = 1
  await fetchProducts()
}

async function handleStatusToggle(id: string) {
  if (isTogglingStatus.value) return

  const result = await toggleProductStatus(id)

  if (result) {
    await fetchProducts()
  } else if (storeError.value) {
    toast.error(storeError.value)
  }
}

async function handleDelete(id: string) {
  if (isDeleting.value) return

  const result = await deleteProduct(id)

  if (result) {
    await fetchProducts()
  } else if (storeError.value) {
    toast.error(storeError.value)
  }
}

async function resetFilters() {
  searchQuery.value = ''
  statusFilter.value = 'all'
  mainCategoryFilter.value = 'all'
  sortBy.value = { column: '', direction: 'desc' }
  pagination.value.currentPage = 1
  pagination.value.perPage = 10
  await fetchProducts()
}

// function navigateToEdit(id: string) {
//   router.push({ name: 'admin-products-edit', params: { id } })
// }

function navigateToView(id: string) {
  router.push({ name: 'admin-products-view', params: { id } })
}

function navigateToAdd() {
  router.push({ name: 'admin-products-add' })
}

// function navigateToOverview(id: string) {
//   router.push({ name: 'admin-products-overview', params: { id } })
// }

watch(
  () => debouncedSearchQuery.value,
  () => {
    handleSearch()
  },
)

onMounted(async () => {
  await Promise.all([fetchProducts(), fetchCategoryOptions()])
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-col gap-4 sm:flex-row sm:gap-0 sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">Products</h1>
        <p class="text-muted-foreground text-sm">Manage your product catalog</p>
      </div>
      <Button @click="navigateToAdd">
        <PlusIcon class="h-4 w-4" />
        Add Product
      </Button>
    </div>

    <Card>
      <CardHeader class="pb-3">
        <div
          class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-x-4 sm:space-y-0"
        >
          <div class="relative w-full sm:max-w-sm">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="searchQuery"
              placeholder="Search"
              class="pl-8"
              @keyup.enter="handleSearch"
            />
            <XIcon
              v-if="searchQuery"
              class="absolute right-2.5 top-2.5 h-4 w-4 cursor-pointer text-muted-foreground"
              @click="searchQuery = ''"
            />
          </div>
          <div class="flex items-center space-x-2">
            <Select
              v-if="categoryOptions.length"
              v-model="mainCategoryFilter"
              @update:model-value="handleSearch"
            >
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem
                  v-for="category in categoryOptions"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}{{ category.gender ? ` (${category.gender})` : '' }}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select v-model="statusFilter" @update:model-value="handleSearch">
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="1">Active</SelectItem>
                <SelectItem value="0">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" @click="resetFilters"> Reset </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="min-w-[150px]">Product</TableHead>
                <TableHead class="min-w-[150px]">Category</TableHead>
                <TableHead class="min-w-[150px]">Starting Price</TableHead>
                <TableHead class="min-w-[150px]">Status</TableHead>
                <TableHead
                  class="cursor-pointer hover:bg-muted/50 min-w-[150px]"
                  @click="handleSort('created_at')"
                >
                  <div class="flex items-center">
                    Created At
                    <span v-if="sortBy.column === 'created_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead
                  class="cursor-pointer hover:bg-muted/50 min-w-[150px]"
                  @click="handleSort('updated_at')"
                >
                  <div class="flex items-center">
                    Updated At
                    <span v-if="sortBy.column === 'updated_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead class="min-w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <!-- Loading -->
              <template v-if="isLoadingList">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center"> Loading... </TableCell>
                </TableRow>
              </template>

              <!-- No results -->
              <template v-else-if="products.length === 0">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center py-6">
                    <div class="flex flex-col items-center gap-4">
                      <div class="p-4 rounded-full bg-muted/50">
                        <IconBox class="h-8 w-8 text-muted-foreground" stroke-width="1.5" />
                      </div>
                      <h4 class="text-lg font-semibold text-muted-foreground">No Products found</h4>
                      <Button @click="navigateToAdd">Add Product</Button>
                    </div>
                  </TableCell>
                </TableRow>
              </template>

              <!-- Data -->
              <template v-else>
                <TableRow v-for="product in products" :key="product.id">
                  <TableCell>
                    <div class="flex items-center gap-3">
                      <div
                        class="min-w-12 w-12 h-12 rounded-md overflow-hidden bg-muted cursor-pointer"
                        @click="navigateToView(product.id)"
                      >
                        <img
                          v-if="product.image"
                          :src="product.image"
                          :alt="product.product_name_title || 'Product'"
                          class="w-full h-full object-cover"
                        />
                        <div
                          v-else
                          class="w-full h-full flex items-center justify-center text-xs text-muted-foreground"
                        >
                          No Image
                        </div>
                      </div>
                      <div class="text-wrap max-w-xs">
                        <div
                          class="font-medium line-clamp-2 cursor-pointer"
                          @click="navigateToView(product.id)"
                        >
                          {{ product.product_name_title || 'Untitled' }}
                        </div>
                        <div
                          class="text-xs text-muted-foreground line-clamp-1 cursor-pointer"
                          @click="navigateToView(product.id)"
                        >
                          {{ product.slug }}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="font-medium">{{ product.main_category_name }}</div>
                    <div v-if="product.sub_category_name" class="text-xs text-muted-foreground">
                      {{ product.sub_category_name }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="font-medium">{{ formatCurrency(product.product_price) }}</div>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-2">
                      <Switch
                        v-model="product.status"
                        :disabled="isLoadingList"
                        @click="handleStatusToggle(product.id)"
                      />
                      <span :class="product.status ? 'text-green-600' : 'text-muted-foreground'">
                        {{ product.status ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ product.created_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ product.created_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ product.updated_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ product.updated_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="flex">
                      <Button
                        variant="ghost"
                        size="icon"
                        @click="navigateToView(product.id)"
                        title="View Product"
                      >
                        <Eye class="h-4 w-4" />
                      </Button>
                      <!-- <Button
                        variant="ghost"
                        size="icon"
                        @click="navigateToOverview(product.id)"
                        title="Product Overview"
                      >
                        <FileText class="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        @click="navigateToEdit(product.id)"
                        title="Edit Product"
                      >
                        <EditIcon class="h-4 w-4" />
                      </Button> -->
                      <Button
                        variant="ghost"
                        size="icon"
                        @click="
                          () => {
                            selectedProductId = product.id
                            isDeleteDialogOpen = true
                          }
                        "
                        title="Delete Product"
                      >
                        <Trash2Icon class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </template>
            </TableBody>
          </Table>
        </div>

        <!-- Pagination -->
        <div
          class="flex flex-col gap-4 sm:flex-row sm:gap-0 items-center justify-between px-2 mt-4"
        >
          <div class="flex items-center space-x-2">
            <p class="text-sm text-muted-foreground">
              Page {{ pagination.currentPage }} of {{ pagination.totalPages }}
            </p>
            <Select :model-value="pagination.perPage" @update:model-value="handlePerPageChange">
              <SelectTrigger class="h-8">
                <SelectValue :placeholder="pagination.perPage.toString()" />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem
                  v-for="pageSize in [10, 25, 50, 100]"
                  :key="pageSize"
                  :value="pageSize"
                  class="cursor-pointer"
                >
                  {{ pageSize }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div v-if="products.length" class="text-sm text-muted-foreground">
            Showing {{ products.length }} of {{ pagination.totalRecords }} products
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage === 1"
              @click="handlePageChange(pagination.currentPage - 1)"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage >= pagination.totalPages"
              @click="handlePageChange(pagination.currentPage + 1)"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Delete Confirmation Dialog -->
    <ConfirmationDialog
      v-model:open="isDeleteDialogOpen"
      title="Are you absolutely sure?"
      description="This action will delete the product and all its data. This action cannot be undone."
      confirmText="Delete"
      @confirm="handleDelete(selectedProductId)"
    />
  </div>
</template>
