<?php

return [
    "authentication_success" => "User authenticated successfully.",
    "2fa_authentication_verified"  => "2FA code verified successfully.",
    // forgot and reset password
    'forgot_password_success' => 'Password reset instructions have been sent to your email address.',
    'new_password_update'    => 'We have updated your password successfully!',
    'reset'    => 'We have reset your password successfully!',

    // category
    'add_main_category_success'   => 'The category has been successfully added.',
    'update_main_category_success' => 'The category has been updated successfully.',
    'update_main_category_image_success' => 'The image has been updated successfully.',
    'main_category_is_active_status_active' => 'The category has been activated successfully.',
    'main_category_is_active_status_inactive' => 'The category has been marked as inactive.',
    'delete_main_category_success' => 'The category has been successfully deleted.',

    // sub category
    'add_sub_category_success'   => 'The sub category has been successfully added.',
    'update_sub_category_success' => 'The sub category has been updated successfully.',
    'sub_category_is_active_status_active' => 'The sub category has been activated successfully.',
    'sub_category_is_active_status_inactive' => 'The sub category has been marked as inactive.',
    'delete_sub_category_success' => 'The sub category has been successfully deleted.',

    // product
    'add_product_success'   => 'The product has been successfully added.',
    'update_product_success' => 'The product has been updated successfully.',
    'update_product_image_success' => 'The image has been updated successfully.',
    'product_is_active_status_active' => 'The product has been activated successfully.',
    'product_is_active_status_inactive' => 'The product has been marked as inactive.',
    'delete_product_success' => 'The product has been successfully deleted.',
    'product_stock_available_status_updated' => 'The stock availability has been updated successfully.',


    "setting_update_success" => "Settings updated successfully.",
    "2fa_enabled_success"  => "2FA enabled successfully.",
    "2fa_disabled_success"  => "2FA disabled successfully.",

    "top_product_mapping_success" => "The product has been mapped successfully.",
    "top_product_unmapping_success" => "The product has been unmapped successfully.",
    "top_product_reorder_success" => "The product has been reordered successfully.",
    "product_detail_update_success" => "The product details has been updated successfully.",

    "plan_details_updated_successfully" => "The plan details has been updated successfully.",
    "image_updated_successfully" => "The image has been updated successfully.",

    "pharmacy_details_updated_successfully" => "The pharmacy details has been updated successfully.",
];
