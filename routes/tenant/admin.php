<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Route::get('/user', function (Request $request) {
//     return $request->user();
// })->middleware('auth:api');

Route::controller(\App\Http\Controllers\Tenant\API\Admin\AuthController::class)->group(function () {
    Route::post('login', 'AdminLogin');
    Route::get('verify/account/setup/{token}', 'VerifyAccountSetup')->middleware('tenant-guest');
    Route::post('setup/account/password', 'SetupPassword')->middleware('tenant-guest');
});

Route::group(['middleware' => ['tenant-sanctum-cookie','tenant-auth-sanctum']], function () {
    Route::controller(\App\Http\Controllers\Tenant\API\Admin\AuthController::class)->group(function () {
        Route::get('verify-token', 'VerifyToken');
        Route::post('change-password', 'ChangePassword');
        Route::get('logout', 'logout');
    });

    // main category
    Route::controller(App\Http\Controllers\Tenant\API\Admin\CategoryController::class)->group(function () {
        Route::post('main-category/add' , 'AddCategory');
        Route::post('main-category/list' , 'ListCategory');
        Route::get('main-category/view/{id}' , 'ViewCategoryById');
        Route::post('main-category/edit' , 'EditCategory');
        Route::delete('main-category/delete/{id}' , 'DeleteCategory');
        Route::get('main-category/update/status/{id}' , 'UpdateCategoryStatus');
        Route::post('update-main-category-image' , 'UpdateMainCategoryImage');
        Route::get('main-category/all' , 'MainCategoryList');
    });

    // sub category
    Route::controller(App\Http\Controllers\Tenant\API\Admin\SubCategoryController::class)->group(function () {
        Route::post('sub-category/add' , 'AddSubCategory');
        Route::post('sub-category/list' , 'ListSubCategory');
        Route::get('sub-category/view/{id}' , 'ViewSubCategoryById');
        Route::post('sub-category/edit' , 'EditSubCategory');
        Route::delete('sub-category/delete/{id}' , 'DeleteSubCategory');
        Route::get('sub-category/update/status/{id}' , 'UpdateSubCategoryStatus');
    });

    // Product
    Route::controller(App\Http\Controllers\Tenant\API\Admin\ProductController::class)->group(function () {
        Route::get('all-categories' , 'AllCategories');
        Route::post('product/add' , 'AddProduct');
        Route::post('product/list' , 'ListProduct');
        Route::get('product/view/{productId}' , 'ViewProductById');
        Route::post('product/edit' , 'EditProduct');
        Route::delete('product/delete/{id}' , 'DeleteProduct');
        Route::get('product/update/status/{id}' , 'UpdateProductStatus');
        Route::get('product-stock-availability/status/{id}' , 'UpdateProductStockAvailabilityStatus');
        Route::post('update-product-image' , 'UpdateProductImage');
        Route::post('update-product-descriptions-faqs' , 'UpdateProductDescriptionFaqs');
    });


    // Top Product
    Route::controller(App\Http\Controllers\Tenant\API\Admin\TopProductMappingController::class)->group(function () {
        Route::get('all-products' , 'AllProducts');
        Route::get('add/top-product/{id}' , 'AddTopProduct');
        Route::get('list/top-products' , 'ListTopProduct');
        Route::get('delete/top-product/{id}' , 'DeleteTopProduct');
        Route::post('re-order/top-products' , 'ReOrderTopProduct');
    });

    Route::controller(\App\Http\Controllers\Tenant\API\Admin\TenantContactController::class)->group(function () {
        Route::post('contacts', 'ListContacts');
        Route::get('view/product/details/{id}', 'ViewContactDetails');
    });

    // main category
    Route::controller(App\Http\Controllers\Tenant\API\Admin\AppSettingController::class)->group(function () {
        Route::get('app-settings' , "AppSettings");
        Route::post('update-app-settings' , 'UpdateAppSetting');
        Route::post('update-app-logo' , "UpdateAppLogo");
        Route::post('update-app-favicon' , "UpdateAppFavicon");
        // Route::post('update-app-policy-urls' , 'UpdatePolicyUrl');
        // Route::post('update-app-social-urls' , 'UpdateSocialUrl');
        // Route::post('update-app-support-details' , 'UpdateAppSupportDetails');
        Route::post("update-app-configs" ,  "UpdateAppConfigs");
        Route::post("update-onboarding-app-configs" ,  "UpdateOnboardingAppSetting");
    });
});

