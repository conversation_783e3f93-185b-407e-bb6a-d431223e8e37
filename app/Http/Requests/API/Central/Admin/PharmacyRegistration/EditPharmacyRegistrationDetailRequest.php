<?php

namespace App\Http\Requests\API\Central\Admin\PharmacyRegistration;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EditPharmacyRegistrationDetailRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "id" => ['required'],
            'pharmacy_name' => ['required', 'string', 'max:100'],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'email' => ['required', 'email', 'max:150',Rule::unique('pharmacy_registrations')->ignore($this->id)],
            'phone_number' => ['required', 'digits:10',Rule::unique('pharmacy_registrations')->ignore($this->id)],
            'preferred_sub_domain' => ['required', 'string', 'max:100', 'regex:/^[A-Za-z0-9-]+$/']
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422, 'message' => $validator->errors()->first(), 'errors' => $validator->errors()]));
    }
}
