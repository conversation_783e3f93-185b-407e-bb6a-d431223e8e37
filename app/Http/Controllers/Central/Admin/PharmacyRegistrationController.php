<?php

namespace App\Http\Controllers\Central\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Central\Admin\PharmacyRegistration\EditPharmacyRegistrationDetailRequest;
use App\Models\PaymentLog;
use App\Models\PharmacyRegistration;
use App\Models\Tenant;
use App\Traits\DateTrait;
use App\Traits\TenantTrait;
use Illuminate\Http\Request;
use App\Utils\PaginateCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PharmacyRegistrationController extends Controller
{
    use DateTrait;

    public function ListPharmacyRegistration(Request $request){
        try {
            $perpage = (@$request->perPage) ? $request->perPage : 10;
            $pharmacies = PharmacyRegistration::select('id','pharmacy_name','first_name','last_name','email','phone_number','preferred_sub_domain','status','created_at','updated_at');

            if($request->status!=''){
                $pharmacies->where('status',$request->status);
            }
            if($request->searchQuery!=''){
                $search = strtolower($request->searchQuery);
                $pharmacies = $pharmacies->where(function($q) use($search){
                    $q->where('pharmacy_name', 'LIKE', "%" . $search . "%")
                        ->orWhere('email', 'LIKE', "%" . $search . "%")
                        ->orWhere('phone_number', 'LIKE', "%" . $search . "%")
                        ->orWhere('preferred_sub_domain', 'LIKE', "%" . $search . "%")
                        ->orWhere(DB::raw("CONCAT(first_name,' ',last_name)"), 'LIKE', "%{$search}%");
                });
            }

            if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
                $pharmacies = $pharmacies->orderBy($request->sortByColumnName,$request->isSortDirDesc);
            } else {
                $pharmacies = $pharmacies->latest();
            }

            $pharmacies = $pharmacies->paginate($perpage);
            $timezone = $this->GetUserTimezone($request);
            $pharmacies->map(function ($pharmacies) use($timezone){
                $pharmacies->created_date = $this->GetDate($pharmacies->created_at,$timezone);
                $pharmacies->created_time = $this->GetTime($pharmacies->created_at,$timezone);
                $pharmacies->updated_date = $this->GetDate($pharmacies->updated_at,$timezone);
                $pharmacies->updated_time = $this->GetTime($pharmacies->updated_at,$timezone);
                unset($pharmacies->created_at,$pharmacies->updated_at);
                return $pharmacies;
            });
            $info = PaginateCollection::paginate($pharmacies);
            return response()->json([
                'status' =>200,
                'pharmacies' => $info
            ]);

        } catch (\Throwable $th) {
            Log::error('An error occurred while listing pharmacy registrations data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ViewPharmacyRegistrationDetails($id,Request $request){
        try {
            $pharmacy = PharmacyRegistration::select('id','pharmacy_name','first_name','last_name','email','phone_number','preferred_sub_domain','address_details','status','last_card_four_digits','card_expiry_month','card_expiry_year','card_brand_type')
                    ->where('id',$id)
                    ->first();
            if($pharmacy){
                $payment_logs = PaymentLog::select('id','authorize_transaction_id','payment_capture_id','plan_amount','payment_status','transaction_type','last_card_four_digits','card_brand_type','created_at')
                        ->where('pharmacy_registration_id',$pharmacy->id)
                        ->latest()
                        ->get();
                $timezone = $this->GetUserTimezone($request);
                $payment_logs->map(function ($payment_logs) use($timezone){
                    $payment_logs->transaction_id = ($payment_logs->transaction_type == 'authorize') ? $payment_logs->authorize_transaction_id : $payment_logs->payment_capture_id;
                    $payment_logs->created_date = $this->GetDate($payment_logs->created_at,$timezone);
                    $payment_logs->created_time = $this->GetTime($payment_logs->created_at,$timezone);
                    unset($payment_logs->created_at,$payment_logs->payment_capture_id,$payment_logs->authorize_transaction_id);
                    return $payment_logs;
                });
                $pharmacy->payment_logs = $payment_logs;
                return response()->json([
                    'status' =>200,
                    'pharmacyDetails' => $pharmacy
                ]);
            } else {
                return response()->json([
                    'status' =>400,
                    'message' => trans('centraladmin/errors.record_not_found')
                ]);
            }

        } catch (\Throwable $th) {
            Log::error('An error occurred while view pharmacy registration details data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function GetPharmacyRegistrationPaymentLogs(Request $request){
        try {
            $perpage = (@$request->perPage) ? $request->perPage : 10;
            $payment_logs = PaymentLog::select('id','authorize_transaction_id','payment_capture_id','plan_amount','payment_status','transaction_type','last_card_four_digits','card_brand_type','created_at')
                        ->where('pharmacy_registration_id',$request->id);

            if($request->payment_status!=''){
                $payment_logs->where('payment_status',$request->payment_status);
            }
            if($request->searchQuery!=''){
                $search = strtolower($request->searchQuery);
                $payment_logs = $payment_logs->where(function($q) use($search){
                    $q->where('authorize_transaction_id', 'LIKE', "%" . $search . "%")
                        ->orWhere('payment_capture_id', 'LIKE', "%" . $search . "%");
                });
            }

            if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
                $payment_logs = $payment_logs->orderBy($request->sortByColumnName,$request->isSortDirDesc);
            } else {
                $payment_logs = $payment_logs->latest();
            }

            $payment_logs = $payment_logs->paginate($perpage);
            $timezone = $this->GetUserTimezone($request);
            $payment_logs->map(function ($payment_logs) use($timezone){
                $payment_logs->transaction_id = ($payment_logs->transaction_type == 'authorize') ? $payment_logs->authorize_transaction_id : $payment_logs->payment_capture_id;
                $payment_logs->created_date = $this->GetDate($payment_logs->created_at,$timezone);
                $payment_logs->created_time = $this->GetTime($payment_logs->created_at,$timezone);
                unset($payment_logs->created_at,$payment_logs->payment_capture_id,$payment_logs->authorize_transaction_id);
                return $payment_logs;
            });
            $info = PaginateCollection::paginate($payment_logs);
            return response()->json([
                'status' =>200,
                'paymentLogs' => $info
            ]);

        } catch (\Throwable $th) {
            Log::error('An error occurred while listing pharmacy registrations data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ViewPaymentLog($id,Request $request){
        try {
            $payment_log = PaymentLog::select('id','authorize_transaction_id','authorize_transaction_expired_at','payment_capture_id','plan_amount','payment_status','transaction_type','last_card_four_digits','failed_reason','card_brand_type','created_at')
                    ->where('id',$id)
                    ->first();
            if($payment_log){
                $timezone = $this->GetUserTimezone($request);
                $payment_log->created_date = $this->GetDate($payment_log->created_at,$timezone);
                $payment_log->created_time = $this->GetTime($payment_log->created_at,$timezone);
                unset($payment_log->created_at);
                return response()->json([
                    'status' =>200,
                    'paymentLogDetails' => $payment_log
                ]);
            } else {
                return response()->json([
                    'status' =>400,
                    'message' => trans('centraladmin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while view pharmacy registration details data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function UpdatePharmacyRegistrationDetails(EditPharmacyRegistrationDetailRequest $request){
        try {
            $pharmacy = PharmacyRegistration::select('id')->where('id',$request->id)->first();
            if($pharmacy){
                $pharmacy_name = strtolower(str_replace(' ','-',$request->preferred_sub_domain));
                $result = TenantTrait::CheckPharmacySubdomain($pharmacy_name);
                if($result['status']!=200){
                    return response()->json([
                        'status' =>400,
                        'message' => $request->preferred_sub_domain.' subdomain already taken.'
                    ]);
                }
                $validate_subdomain = PharmacyRegistration::where('id','!=',$pharmacy->id)
                                        ->where('preferred_sub_domain',$pharmacy_name)
                                        ->count();
                if($validate_subdomain>0){
                    return response()->json([
                        'status' =>400,
                        'message' => $request->preferred_sub_domain.' subdomain already taken.'
                    ]);
                }
                $validate_email = Tenant::where('email', $request->email)->count();
                if ($validate_email > 0) {
                    return response()->json([
                        'status' => 400,
                        'message' => "Email already exists"
                    ]);
                }
                $validate_phone = Tenant::where('phone_number', $request->phone_number)->count();
                if ($validate_phone > 0) {
                    return response()->json([
                        'status' => 400,
                        'message' => "Phone number already exists"
                    ]);
                }
                $data = $request->only('pharmacy_name','email','phone_number','first_name','last_name','preferred_sub_domain');
                $pharmacy->update($data);
                return response()->json([
                    'status' =>200,
                    'message' => trans('centraladmin/response.pharmacy_details_updated_successfully')
                ]);
            } else {
                return response()->json([
                    'status' =>400,
                    'message' => trans('centraladmin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while updating pharmacy registration details data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }
}
