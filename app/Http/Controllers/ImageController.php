<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException;

class ImageController extends Controller
{
    public function GetFiles($env,$fold1,$fold2,$fold3,$file){
        // $path = $env.'/'.$fold1.'/'.$fold2.'/'.$fold3.'/'.$file;
        $path = $fold2.'/'.$fold3.'/'.$file;
        if (Storage::disk('local')->exists($path)) {
            try {
                return Storage::disk('local')->get($path);
            } catch (FileNotFoundException $th) {
                return '';
            }
        }
        return '';
    }

    public function GetProductImage($env,$fold1,$fold2,$fold3,$file){
        $path = $env.'/'.$fold1.'/'.$fold2.'/'.$fold3.'/'.$file;
        $path = storage_path($path);
        if (file_exists($path)) {
            return response()->file($path);
        }
        return '';
    }

    public function GetPlan($fold1,$fold2,$file){
        $path = $fold1.'/'.$fold2.'/'.$file;
        if (Storage::disk('local')->exists($path)) {
            try {
                return Storage::disk('local')->get($path);
            } catch (FileNotFoundException $th) {
                return '';
            }
        }
        return '';
    }

    public function GetExcelReport($file){
        $path = 'admin-assets/export-reports/'.$file;
        if (Storage::disk('local')->exists($path)) {
            try {
                return Storage::disk('local')->download($path);
            } catch (FileNotFoundException $th) {
                return '';
            }
        }
        return '';
    }

    public function GetImage($env,$fold1,$fold2,$file){
        // $path = $env.'/'.$fold1.'/'.$fold2.'/'.$file;
        $path = $fold2.'/'.$file;
        if (Storage::disk('local')->exists($path)) {
            try {
                return Storage::disk('local')->get($path);
            } catch (FileNotFoundException $th) {
                return '';
            }
        }
        return '';
    }

    public function GetFrontendImage($env,$fold1,$fold2,$fold3,$file){
        // $path = $env.'/'.$fold1.'/'.$fold2.'/'.$file;
        $path = $fold2.'/'.$fold3.'/'.$file;
        // dd($path);
        if (Storage::disk('local')->exists($path)) {
            try {
                return Storage::disk('local')->get($path);
            } catch (FileNotFoundException $th) {
                return '';
            }
        }
        return '';
    }
}
