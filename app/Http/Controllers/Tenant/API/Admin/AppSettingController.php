<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use App\Traits\FileUploadTrait;
use Illuminate\Http\Request;
use App\Models\Tenant\AppSetting;
use App\Http\Requests\API\Tenant\Admin\AppSetting\{ImageValidateRequest, LandingPageRequest, OnboardingAppConfigRequest, UpdateCompanyInformationRequest};
use App\Rules\ValidateImageFile;

class AppSettingController extends Controller
{

    public function AppSettings(Request $request){
        $setting = AppSetting::select('id','app_name','app_logo','app_favicon','lp_data','lp_data as landing_page_data')->first();
        return response()->json([
            "status"=> 200,
            "app_settings"=>$setting
        ]);
    }

    public function UpdateAppConfigs(LandingPageRequest $request){
        try {
            $data = $request->all();
            if (!empty($data['heroCallout']['backgroundImage'])) {
                $image = $data['heroCallout']['backgroundImage'];
                if (filter_var($image, FILTER_VALIDATE_URL)) {
                    $data['heroCallout']['backgroundImage'] = self::removeImageBasePath($image);
                } else {
                    $validator = \Validator::make(
                        ['backgroundImage' => $image],
                        ['backgroundImage' => [new ValidateImageFile()]]
                    );

                    if ($validator->fails()) {
                        return response()->json([
                            'status'=> 422,
                            'message' => $validator->errors()->first('backgroundImage')
                        ]);
                    }
                    $data['heroCallout']['backgroundImage'] = FileUploadTrait::UploadLandingPageImages($image);
                }
            }
            if (!empty($data['fixItTogether']['cards']) && count($data['fixItTogether']['cards']) > 0) {
                foreach ($data['fixItTogether']['cards'] as &$card) {
                    if (!empty($card['backgroundImage'])) {
                        if (!empty($card['backgroundImage'])) {
                            $image = $card['backgroundImage'];
                            if (filter_var($image, FILTER_VALIDATE_URL)) {
                                $card['backgroundImage'] = self::removeImageBasePath($image);
                            } else {
                                $validator = \Validator::make(
                                    ['backgroundImage' => $image],
                                    ['backgroundImage' => [new ValidateImageFile()]]
                                );

                                if ($validator->fails()) {
                                    return response()->json([
                                        'status'=> 422,
                                        'message' => $validator->errors()->first('backgroundImage')
                                    ]);
                                }
                                $card['backgroundImage'] = FileUploadTrait::UploadLandingPageImages($image);
                            }
                        }
                    }
                }
            }
            $setting = AppSetting::select('id')->first();
            if($setting){
                $setting->update(['lp_data' => json_encode($data)]);
            } else {
                return response()->json([
                    'status' => 500,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.setting_update_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while updating app configs ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message'=> trans('admin/errors.server_error')
            ]);
        }
    }

    protected static function removeImageBasePath($image){
        if(env('APP_ENV') == 'local'){
            return str_replace(env('APP_URL').'/images/', '', $image);
        } else {
            return str_replace(env('APP_URL') . '/' . config('tenancy.filesystem.suffix_base').tenant()->id.'/', '', $image);

        }
    }

    public function UpdateAppSetting(UpdateCompanyInformationRequest $request){
        try {
            $data = $request->only('app_name');
            $setting = AppSetting::select('id')->first();
            if($setting){
                $setting->update($data);
            } else {
                AppSetting::create($data);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.setting_update_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while update app setting ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateOnboardingAppSetting(OnboardingAppConfigRequest $request){
        try {
            $data = $request->only('app_name');
            if($request->app_logo){
                $data['app_logo'] = FileUploadTrait::UploadBase64Image($request->app_logo,'app_settings',1);
            }
            if($request->app_favicon){
                $data['app_favicon'] = FileUploadTrait::UploadBase64Image($request->app_favicon,'app_settings',0);
            }
            $setting = AppSetting::select('id')->first();
            if($setting){
                $setting->update($data);
            } else {
                AppSetting::create($data);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.setting_update_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while update onboarding app setting ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateAppLogo(ImageValidateRequest $request){
        try {
            $data['app_logo'] = FileUploadTrait::UploadBase64Image($request->image,'app_settings',1);
            $setting = AppSetting::select('id')->first();
            if($setting){
                $setting->update($data);
            } else {
                AppSetting::create($data);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.setting_update_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while updating app logo ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateAppFavicon(ImageValidateRequest $request){
        try {
            $data['app_favicon'] = FileUploadTrait::UploadBase64Image($request->image,'app_settings',0);
            $setting = AppSetting::select('id')->first();
            if($setting){
                $setting->update($data);
            } else {
                AppSetting::create($data);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.setting_update_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while updating app logo ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }
}
