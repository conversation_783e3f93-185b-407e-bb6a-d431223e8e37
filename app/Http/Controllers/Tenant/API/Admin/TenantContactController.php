<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant\Contact;
use App\Traits\DateTrait;
use Illuminate\Http\Request;
use App\Utils\PaginateCollection;
use Illuminate\Support\Facades\Log;

class TenantContactController extends Controller
{
    use DateTrait;

    public function ListContacts(Request $request){
        try {
            $perpage = (@$request->perPage) ? $request->perPage : 10;
            $products = Contact::select('id','name','email','phone_number','product_details','created_at');

            if($request->searchQuery!=''){
                $search = strtolower($request->searchQuery);
                $products = $products->where(function ($q) use ($search) {
                    $q->whereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_title'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_type'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.main_category_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.sub_category_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(phone_number) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(email) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(name) LIKE ?", ["%{$search}%"]);
                });
            }

            if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
                $products = $products->orderBy($request->sortByColumnName,$request->isSortDirDesc);
            } else {
                $products = $products->latest();
            }

            $products = $products->paginate($perpage);
            $timezone = $this->GetUserTimezone($request);
            $products->map(function ($products) use($timezone){
                $product_details = $products->product_details;
                $product = null;
                if($product_details){
                    $product['main_category_name'] = $product_details['main_category_name'];
                    $product['sub_category_name'] = $product_details['sub_category_name'] ?? null;
                    $product['product_name'] = $product_details['product_name'];
                    if($product_details['product_type'] == 'programs'){
                        $product['product_title'] = $product_details['product_title'];
                    }
                    $product['product_strength'] = $product_details['product_strength'];
                    $product['product_type'] = $product_details['product_type'];
                    $product['product_qty'] = $product_details['product_qty'];
                    $product['product_form'] = $product_details['product_form'];
                    $product['price'] = $product_details['price'];
                }
                $products->product_detail = $product;

                $products->created_date = $this->GetDate($products->created_at,$timezone);
                $products->created_time = $this->GetTime($products->created_at,$timezone);
                unset($products->created_at,$products->product_details);
                return $products;
            });
            $info = PaginateCollection::paginate($products);
            return response()->json([
                'status' =>200,
                'contacts' => $info
            ]);

        } catch (\Throwable $th) {
            Log::error('An error occurred while listing pharmacy registrations on tenant domain',[
                'Errors'=>$th
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ViewContactDetails($id,Request $request){
        try {
            $product_detail = Contact::select('id','name','email','phone_number','product_details','created_at')
                        ->where('id',$id)
                        ->first();
            if($product_detail){
                $product = null;
                $product_details = $product_detail->product_details;
                if($product_details){
                    $product['main_category_name'] = $product_details['main_category_name'];
                    $product['sub_category_name'] = $product_details['sub_category_name'] ?? null;
                    $product['product_name'] = $product_details['product_name'];
                    if($product_details['product_type'] == 'programs'){
                        $product['product_title'] = $product_details['product_title'];
                    }
                    $product['product_strength'] = $product_details['product_strength'];
                    $product['product_type'] = $product_details['product_type'];
                    $product['product_qty'] = $product_details['product_qty'];
                    $product['product_form'] = $product_details['product_form'];
                    $product['price'] = $product_details['price'];
                    $product['xpedicare_url'] = $product_details['xpedicare_url'];
                    $image = new Contact();
                    $product['image'] = $image->GetImage($product_details['image'],$product_detail->tenant_id);
                }
                $product_detail->product_detail = $product;
                $timezone = $this->GetUserTimezone($request);
                $product_detail->created_date = $this->GetDate($product_detail->created_at,$timezone);
                $product_detail->created_time = $this->GetTime($product_detail->created_at,timezone: $timezone);
                unset($product_detail->product_details,$product_detail->created_at);
                return response()->json([
                    'status' =>200,
                    'contactDetails' => $product_detail
                ]);
            } else {
                return response()->json([
                    'status' =>400,
                    'message' => trans('centraladmin/errors.record_not_found')
                ]);
            }

        } catch (\Throwable $th) {
            Log::error('An error occurred while view pharmacy registration details data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }
}
