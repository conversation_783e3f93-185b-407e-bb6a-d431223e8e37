<?php

namespace App\Models\Tenant;

use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Contact extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public function product(){
        return $this->belongsTo(Product::class);
    }

    public function getProductDetailsAttribute($product_details){
        return ($product_details) ? json_decode($product_details,true) : null;
    }

    public function getImage($image){
        if(empty($image)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($image,'products');
        }
    }
}
