<?php

namespace App\Jobs\Tenant;

use App\Models\Tenant\UserAuthActivityLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class AdminUserLogActivityJob implements ShouldQueue
{
    use Queueable;

    public $user_details;

    /**
     * Create a new job instance.
     */
    public function __construct($user_details)
    {
        $this->user_details = $user_details;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        tenancy()->initialize($this->user_details['tenant']);
        $user_details = $this->user_details;
        $agent = $user_details['agent'];
        $ip = $user_details['ip'];
        $result = UserAuthActivityLog::log($user_details['user_id'], 1, 'login',$agent,$ip);
        DB::table('personal_access_tokens')
                    ->where('id', $user_details['token_id'])
                    ->update(['user_timezone'=>$result['timezone']]);
        tenancy()->end();

    }
}

