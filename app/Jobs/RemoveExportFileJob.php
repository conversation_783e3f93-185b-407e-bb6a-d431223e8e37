<?php

namespace App\Jobs;

use App\Traits\FileUploadTrait;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class RemoveExportFileJob implements ShouldQueue
{
    use Queueable;

    protected $path;
    /**
     * Create a new job instance.
     */
    public function __construct($path)
    {
        $this->path = $path;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        FileUploadTrait::RemoveExcelFile($this->path);
    }
}
