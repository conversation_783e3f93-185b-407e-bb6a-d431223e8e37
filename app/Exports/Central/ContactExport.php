<?php
namespace App\Exports\Central;

use App\Models\Contact;
use App\Traits\DateTrait;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\{
    FromCollection,
    WithHeadings,
    WithMapping,
    WithChunkReading,
    WithStyles,
    ShouldAutoSize,
    WithEvents
};
use Maatwebsite\Excel\Concerns\Exportable;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;

class ContactExport implements FromCollection, WithHeadings, WithMapping, WithChunkReading, WithStyles, ShouldAutoSize, WithEvents
{
    use Exportable, DateTrait;

    private $counter = 0;
    private $timezone;
    private $request;
    private $data;

    public function __construct($timezone = 'UTC', $request=null)
    {
        $this->timezone = $timezone;
        $this->request = $request;
    }

    public function collection()
    {
        $query = Contact::query()->select('tenant_id','name', 'email','phone_number','product_details', 'created_at')
            ->whereHas('pharmacy_details')
            ->with('pharmacy_details:id,pharmacy_name');

        $request = $this->request;

        if($request->from_date!='' && $request->to_date!=''){
            $from_date = Carbon::parse($request->from_date)->format('Y-m-d');
            $to_date = Carbon::parse($request->to_date)->format('Y-m-d');
            $query->whereDate('created_at','>=',$from_date)->whereDate('created_at','<=',$to_date);
        } else if($request->from_date!=''){
            $from_date = Carbon::parse($request->from_date)->format('Y-m-d');
            $query->whereDate('created_at','>=',$from_date)->whereDate('created_at','<=',$from_date);
        }

        if(!empty($request->pharmacy_id)){
            $query->where('tenant_id',$request->pharmacy_id);
        }

        $this->data = $query->get();

        // If no data, return collection with one dummy row for "No Contact Found"
        if ($this->data->isEmpty()) {
            return collect([['No Contact Found']]);
        }

        return $this->data;
    }

    public function headings(): array
    {
        return [
            '#',
            'Pharmacy Name',
            'Name',
            'Email',
            'Phone Number',
            'Main Category Name',
            'Sub Category Name',
            'Product Name',
            'Product Title',
            'Product Form',
            'Product Strength',
            'Product Qty',
            'Product Type',
            'Price',
            'Xpedicare URL',
            'Created At'
        ];
    }

    public function map($contact): array
    {
        // Check for "No Contact Found" dummy row
        if (isset($contact[0]) && $contact[0] === 'No Contact Found') {
            return array_merge([''], array_fill(1, 15, '')); // empty columns except first
        }

        $this->counter++;
        return [
            $this->counter,
            $contact->pharmacy_details->pharmacy_name ?? '-',
            $contact->name,
            $contact->email,
            $contact->phone_number,
            $contact->product_details['main_category_name'] ?? '-',
            $contact->product_details['sub_category_name'] ?? '-',
            $contact->product_details['product_name'] ?? '-',
            $contact->product_details['product_title'] ?? '-',
            $contact->product_details['product_form'] ?? '-',
            $contact->product_details['product_strength'] ?? '-',
            $contact->product_details['product_qty'] ?? '-',
            $contact->product_details['product_type'] ?? '-',
            '$'.$contact->product_details['price'] ?? '-',
            $contact->product_details['xpedicare_url'] ?? '-',
            $this->GetDateTime($contact->created_at, $this->timezone)
        ];
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('1')->getFont()->setBold(true); // Bold headers
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();
                $highestRow = $sheet->getHighestRow();

                // If no real data, merge row 2 and center "No Contact Found"
                if ($this->counter === 0) {
                    $lastColumn = 'P'; // total 16 columns
                    $sheet->mergeCells("A2:{$lastColumn}2");
                    $sheet->setCellValue('A2', 'No Contact Found');
                    $sheet->getStyle('A2')->getAlignment()
                          ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                }
            }
        ];
    }
}
